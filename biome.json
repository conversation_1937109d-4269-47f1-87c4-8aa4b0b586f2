{"$schema": "https://biomejs.dev/schemas/2.1.1/schema.json", "files": {"ignoreUnknown": false, "maxSize": 1048576, "experimentalScannerIgnores": ["node_modules", ".next", "dist", "build", "coverage", ".turbo", "public/~partytown", "data", "google-ads-data"], "includes": ["**", "!.next/**", "!node_modules/**", "!dist/**", "!build/**", "!coverage/**", "!.turbo/**", "!tsconfig.tsbuildinfo", "!pnpm-lock.yaml", "!package-lock.json", "!yarn.lock", "!public/~partytown/**", "!data/**/*.js", "!**/*.min.js", "!**/*.generated.*", "!docs/**", "!google-ads-data/**"]}, "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "linter": {"enabled": true, "domains": {"next": "recommended", "react": "recommended"}, "rules": {"style": "off", "a11y": "off", "complexity": {"recommended": false, "noUselessFragments": "off"}, "nursery": "off", "correctness": {"recommended": true, "useExhaustiveDependencies": "warn"}, "security": {"noDangerouslySetInnerHtml": "off"}, "performance": "warn", "suspicious": {"recommended": true, "noExplicitAny": "warn"}}}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 80}, "javascript": {"formatter": {"quoteStyle": "double", "jsxQuoteStyle": "double", "trailingCommas": "es5", "semicolons": "always"}}, "json": {"formatter": {"enabled": true, "indentWidth": 2}}, "assist": {"enabled": true}, "overrides": [{"includes": ["scripts/**"], "linter": {"rules": {"correctness": {"noNodejsModules": "off"}}}}, {"includes": ["app/**/privacy-settings/**", "components/**/Consent*.tsx", "components/**/c15t*.tsx", "components/**/ConsentBridge.tsx"], "linter": {"rules": {"suspicious": {"noDocumentCookie": "off", "noExplicitAny": "off"}}}}]}