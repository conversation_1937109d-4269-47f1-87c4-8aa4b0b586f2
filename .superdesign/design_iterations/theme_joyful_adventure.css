:root {
  --background: #f9faf7;
  --foreground: #2f4f2f;
  --card: #ffffff;
  --card-foreground: #2f4f2f;
  --popover: #ffffff;
  --popover-foreground: #2f4f2f;
  --primary: #3a7a44;
  --primary-foreground: #ffffff;
  --secondary: #f28c28;
  --secondary-foreground: #ffffff;
  --muted: #e6f0e6;
  --muted-foreground: #6b8e23;
  --accent: #f28c28;
  --accent-foreground: #ffffff;
  --destructive: #d94f4f;
  --destructive-foreground: #ffffff;
  --border: #d1d5db;
  --input: #f9faf7;
  --ring: #3a7a44;
  --chart-1: #3a7a44;
  --chart-2: #f28c28;
  --chart-3: #4a90e2;
  --chart-4: #f2a65a;
  --chart-5: #6b8e23;
  --sidebar: #f9faf7;
  --sidebar-foreground: #2f4f2f;
  --sidebar-primary: #3a7a44;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f28c28;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #d1d5db;
  --sidebar-ring: #3a7a44;
  --font-sans: 'Poppins', sans-serif;
  --font-serif: 'Merriweather', serif;
  --font-mono: 'Fira Code', monospace;
  --radius: 0.75rem;
  --shadow-xs: 0 1px 3px rgba(58, 122, 68, 0.1) !important;
  --shadow-sm: 0 1px 5px rgba(58, 122, 68, 0.15) !important;
  --shadow-md: 0 4px 6px rgba(58, 122, 68, 0.2) !important;
  --shadow-lg: 0 10px 15px rgba(58, 122, 68, 0.25) !important;
  --shadow-xl: 0 20px 25px rgba(58, 122, 68, 0.3) !important;
  --tracking-normal: 0em;
  --spacing: 0.5rem;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-serif) !important;
  color: var(--foreground) !important;
}

body {
  font-family: var(--font-sans) !important;
  background-color: var(--background) !important;
  color: var(--foreground) !important;
  margin: 0 !important;
  padding: 0 !important;
}

button, .btn {
  border-radius: var(--radius) !important;
  box-shadow: var(--shadow-sm) !important;
  transition: all 0.3s ease !important;
}

button:hover, .btn:hover {
  box-shadow: var(--shadow-md) !important;
  transform: translateY(-2px) !important;
}

.badge {
  font-family: var(--font-sans) !important;
  font-weight: 600 !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 9999px !important;
  color: var(--primary-foreground) !important;
  background-color: var(--primary) !important;
  box-shadow: var(--shadow-xs) !important;
  display: inline-block !important;
  position: absolute !important;
  top: 0.75rem !important;
  right: 0.75rem !important;
  z-index: 10 !important;
}

.icon {
  color: var(--secondary) !important;
  margin-right: 0.5rem !important;
  vertical-align: middle !important;
}

.card {
  background-color: var(--card) !important;
  border-radius: var(--radius) !important;
  box-shadow: var(--shadow-sm) !important;
  padding: 1.5rem !important;
  position: relative !important;
  transition: box-shadow 0.3s ease, transform 0.3s ease !important;
}

.card:hover {
  box-shadow: var(--shadow-lg) !important;
  transform: translateY(-5px) !important;
}

h1 {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 0.5rem !important;
}

h2 {
  font-size: 1.75rem !important;
  font-weight: 700 !important;
  margin-bottom: 1rem !important;
}

p, li {
  font-size: 1rem !important;
  line-height: 1.5 !important;
  color: var(--foreground) !important;
}

.btn-primary {
  background-color: var(--primary) !important;
  color: var(--primary-foreground) !important;
  border: none !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 700 !important;
  cursor: pointer !important;
  box-shadow: var(--shadow-sm) !important;
  transition: all 0.3s ease !important;
}

.btn-primary:hover {
  background-color: var(--secondary) !important;
  box-shadow: var(--shadow-md) !important;
}

.btn-secondary {
  background-color: transparent !important;
  color: var(--primary) !important;
  border: 2px solid var(--primary) !important;
  padding: 0.5rem 1.25rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}

.btn-secondary:hover {
  background-color: var(--primary) !important;
  color: var(--primary-foreground) !important;
}

.section-highlight {
  background: linear-gradient(90deg, #e6f0e6 0%, #f9faf7 100%) !important;
  padding: 2rem 1rem !important;
  border-radius: var(--radius) !important;
  box-shadow: var(--shadow-xs) !important;
  text-align: center !important;
}

.icon-large {
  font-size: 2rem !important;
  margin-bottom: 0.5rem !important;
  color: var(--secondary) !important;
}

