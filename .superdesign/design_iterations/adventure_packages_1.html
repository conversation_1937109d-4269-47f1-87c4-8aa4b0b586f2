<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Adventure Packages - Pony Club Ecotourism</title>
  <link href="https://fonts.googleapis.com/css2?family=Merriweather&family=Poppins&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="design_iterations/theme_joyful_adventure.css" />
  <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
  <style>
    body {
      margin: 0 !important;
      padding: 0 !important;
      background-color: var(--background) !important;
      color: var(--foreground) !important;
      font-family: var(--font-sans) !important;
    }
    header {
      text-align: center;
      padding: 3rem 1rem 2rem 1rem;
      background: linear-gradient(135deg, #d0e8d0 0%, #f9faf7 100%) !important;
      border-bottom: 2px solid var(--primary) !important;
    }
    header h1 {
      font-family: var(--font-serif) !important;
      font-size: 3rem !important;
      margin-bottom: 0.5rem !important;
      color: var(--primary) !important;
    }
    header p {
      font-size: 1.25rem !important;
      max-width: 600px;
      margin: 0 auto;
      color: var(--foreground) !important;
    }
    main {
      max-width: 1200px;
      margin: 2rem auto 4rem auto;
      padding: 0 1rem;
    }
    .packages-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }
    .card {
      background-color: var(--card) !important;
      border-radius: var(--radius) !important;
      box-shadow: var(--shadow-sm) !important;
      padding: 1.5rem !important;
      position: relative !important;
      transition: box-shadow 0.3s ease, transform 0.3s ease !important;
      display: flex;
      flex-direction: column;
    }
    .card:hover {
      box-shadow: var(--shadow-lg) !important;
      transform: translateY(-5px) !important;
    }
    .badge {
      font-family: var(--font-sans) !important;
      font-weight: 600 !important;
      padding: 0.25rem 0.75rem !important;
      border-radius: 9999px !important;
      color: var(--primary-foreground) !important;
      background-color: var(--primary) !important;
      box-shadow: var(--shadow-xs) !important;
      display: inline-block !important;
      position: absolute !important;
      top: 1rem !important;
      right: 1rem !important;
      z-index: 10 !important;
      font-size: 0.85rem !important;
      text-transform: uppercase !important;
      letter-spacing: 0.05em !important;
    }
    .card img {
      width: 100% !important;
      border-radius: var(--radius) !important;
      object-fit: cover !important;
      max-height: 180px !important;
      margin-bottom: 1rem !important;
    }
    .card h3 {
      font-family: var(--font-serif) !important;
      font-size: 1.5rem !important;
      margin-bottom: 0.25rem !important;
      color: var(--primary) !important;
    }
    .card .tagline {
      font-style: italic !important;
      color: var(--secondary) !important;
      margin-bottom: 1rem !important;
      font-weight: 600 !important;
    }
    .activities-list {
      list-style: none !important;
      padding: 0 !important;
      margin: 0 0 1.5rem 0 !important;
      color: var(--foreground) !important;
      font-weight: 600 !important;
    }
    .activities-list li {
      margin-bottom: 0.5rem !important;
      display: flex !important;
      align-items: center !important;
      font-size: 1rem !important;
    }
    .activities-list li svg {
      margin-right: 0.5rem !important;
      color: var(--secondary) !important;
      min-width: 20px !important;
      min-height: 20px !important;
    }
    .pricing {
      font-weight: 700 !important;
      font-size: 1.25rem !important;
      margin-bottom: 1rem !important;
      color: var(--primary) !important;
    }
    .price-label {
      font-weight: 600 !important;
      font-size: 0.9rem !important;
      color: var(--muted-foreground) !important;
      margin-bottom: 0.25rem !important;
      text-transform: uppercase !important;
      letter-spacing: 0.05em !important;
    }
    .btn-group {
      margin-top: auto !important;
      display: flex !important;
      gap: 1rem !important;
    }
    .btn-primary {
      background-color: var(--primary) !important;
      color: var(--primary-foreground) !important;
      border: none !important;
      padding: 0.75rem 1.5rem !important;
      font-weight: 700 !important;
      cursor: pointer !important;
      box-shadow: var(--shadow-sm) !important;
      transition: all 0.3s ease !important;
      border-radius: var(--radius) !important;
      flex: 1 1 auto !important;
      text-align: center !important;
      text-decoration: none !important;
      display: inline-block !important;
    }
    .btn-primary:hover {
      background-color: var(--secondary) !important;
      box-shadow: var(--shadow-md) !important;
    }
    .btn-secondary {
      background-color: transparent !important;
      color: var(--primary) !important;
      border: 2px solid var(--primary) !important;
      padding: 0.5rem 1.25rem !important;
      font-weight: 600 !important;
      cursor: pointer !important;
      transition: all 0.3s ease !important;
      border-radius: var(--radius) !important;
      flex: 1 1 auto !important;
      text-align: center !important;
      text-decoration: none !important;
      display: inline-block !important;
    }
    .btn-secondary:hover {
      background-color: var(--primary) !important;
      color: var(--primary-foreground) !important;
    }
    .section-highlight {
      background: linear-gradient(90deg, #e6f0e6 0%, #f9faf7 100%) !important;
      padding: 2rem 1rem !important;
      border-radius: var(--radius) !important;
      box-shadow: var(--shadow-xs) !important;
      text-align: center !important;
      margin-bottom: 3rem !important;
    }
    .section-highlight h2 {
      margin-bottom: 1rem !important;
      color: var(--primary) !important;
    }
    .features {
      display: flex !important;
      justify-content: center !important;
      gap: 3rem !important;
      flex-wrap: wrap !important;
    }
    .feature {
      max-width: 200px !important;
      text-align: center !important;
    }
    .feature .icon-large {
      font-size: 2.5rem !important;
      margin-bottom: 0.5rem !important;
      color: var(--secondary) !important;
    }
    .testimonial {
      max-width: 700px !important;
      margin: 0 auto 3rem auto !important;
      font-style: italic !important;
      font-size: 1.25rem !important;
      color: var(--foreground) !important;
      text-align: center !important;
      position: relative !important;
      padding: 1rem 2rem !important;
      border-left: 5px solid var(--primary) !important;
      background-color: var(--muted) !important;
      border-radius: var(--radius) !important;
    }
    .testimonial cite {
      display: block !important;
      margin-top: 1rem !important;
      font-style: normal !important;
      font-weight: 700 !important;
      color: var(--primary) !important;
    }
    .contact {
      max-width: 900px !important;
      margin: 0 auto 3rem auto !important;
      display: flex !important;
      flex-wrap: wrap !important;
      gap: 2rem !important;
      justify-content: space-between !important;
      padding: 0 1rem !important;
    }
    .contact-item {
      flex: 1 1 300px !important;
      display: flex !important;
      align-items: flex-start !important;
      gap: 0.75rem !important;
      color: var(--foreground) !important;
    }
    .contact-item svg {
      color: var(--primary) !important;
      min-width: 24px !important;
      min-height: 24px !important;
      margin-top: 0.25rem !important;
    }
    footer {
      text-align: center !important;
      padding: 1rem !important;
      font-size: 0.875rem !important;
      color: var(--muted-foreground) !important;
      border-top: 1px solid var(--border) !important;
      background-color: var(--background) !important;
    }
    @media (max-width: 600px) {
      header h1 {
        font-size: 2rem !important;
      }
      .packages-grid {
        grid-template-columns: 1fr !important;
      }
      .features {
        flex-direction: column !important;
        gap: 1.5rem !important;
      }
      .contact {
        flex-direction: column !important;
      }
    }
  </style>
</head>
<body>
  <header>
    <h1>Adventure Packages</h1>
    <p>Choose the perfect adventure package for you! We combine different activities at Acheron River for a complete experience you'll remember forever.</p>
  </header>
  <main>
    <section aria-label="Available Adventure Packages">
      <h2>Available Packages</h2>
      <p style="text-align:center; max-width: 600px; margin: 0 auto;">Each package includes a professional guide, complete safety equipment, and unique experiences in nature.</p>
      <div class="packages-grid">
        <article class="card" tabindex="0">
          <div class="badge" aria-label="Most Popular Package">Most Popular</div>
          <img src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=600&q=80" alt="Rafting adventure on river" />
          <h3>Package 1</h3>
          <p class="tagline">Perfect for first-time adventurers</p>
          <ul class="activities-list">
            <li><svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 12l2-2 4 4 8-8 2 2"></path></svg>Rafting: 30 minutes</li>
            <li><svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6L9 17l-5-5"></path></svg>Riding: 10-15 minutes</li>
            <li><svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle></svg>Hiking canyon crossing</li>
          </ul>
          <div class="pricing">
            <div class="price-label">Adults</div>
            20 €
          </div>
          <div class="pricing">
            <div class="price-label">Children (under 12 years old)</div>
            10 €
          </div>
          <div class="btn-group">
            <a href="#" class="btn-secondary" role="button">Learn More</a>
            <a href="#" class="btn-primary" role="button">Book Now</a>
          </div>
        </article>
        <article class="card" tabindex="0">
          <div class="badge" aria-label="Adventurous Package">Adventurous</div>
          <img src="https://images.unsplash.com/photo-1500534623283-312aade485b7?auto=format&fit=crop&w=600&q=80" alt="Kayaking adventure on river" />
          <h3>Package 2</h3>
          <p class="tagline">For thrill seekers and explorers</p>
          <ul class="activities-list">
            <li><svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 12l2-2 4 4 8-8 2 2"></path></svg>Kayak: 30 minutes</li>
            <li><svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6L9 17l-5-5"></path></svg>Riding: 10-15 minutes</li>
            <li><svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle></svg>Hiking canyon crossing</li>
          </ul>
          <div class="pricing">
            <div class="price-label">Adults</div>
            25 €
          </div>
          <div class="pricing">
            <div class="price-label">Children (under 12 years old)</div>
            15 €
          </div>
          <div class="btn-group">
            <a href="#" class="btn-secondary" role="button">Learn More</a>
            <a href="#" class="btn-primary" role="button">Book Now</a>
          </div>
        </article>
        <article class="card" tabindex="0">
          <div class="badge" aria-label="Family Favorite Package">Family Favorite</div>
          <img src="https://images.unsplash.com/photo-1506748686214-e9df14d4d9d0?auto=format&fit=crop&w=600&q=80" alt="Pony riding adventure" />
          <h3>Package 3</h3>
          <p class="tagline">Fun and safe for the whole family</p>
          <ul class="activities-list">
            <li><svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 12l2-2 4 4 8-8 2 2"></path></svg>Pony Riding: 20 minutes</li>
            <li><svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle></svg>Nature Walk</li>
            <li><svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 6L9 17l-5-5"></path></svg>Hiking canyon crossing</li>
          </ul>
          <div class="pricing">
            <div class="price-label">Adults</div>
            18 €
          </div>
          <div class="pricing">
            <div class="price-label">Children (under 12 years old)</div>
            8 €
          </div>
          <div class="btn-group">
            <a href="#" class="btn-secondary" role="button">Learn More</a>
            <a href="#" class="btn-primary" role="button">Book Now</a>
          </div>
        </article>
      </div>
    </section>
    <section class="section-highlight" aria-label="Why Choose Our Packages">
      <h2>Why Choose Our Packages?</h2>
      <div class="features">
        <div class="feature">
          <svg xmlns="http://www.w3.org/2000/svg" class="icon-large" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><path d="M12 2l3 7h7l-5.5 4.5L18 21l-6-4-6 4 1.5-7.5L2 9h7z"></path></svg>
          <h3>Best Value</h3>
          <p>Combine multiple activities in one package and save money</p>
        </div>
        <div class="feature">
          <svg xmlns="http://www.w3.org/2000/svg" class="icon-large" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><circle cx="12" cy="7" r="4"></circle><path d="M5.5 21a6.5 6.5 0 0113 0"></path></svg>
          <h3>Perfect for Families</h3>
          <p>Safe activities for children 6 years and older</p>
        </div>
        <div class="feature">
          <svg xmlns="http://www.w3.org/2000/svg" class="icon-large" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><path d="M12 2a10 10 0 0110 10c0 5.523-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"></path><path d="M12 6v6l4 2"></path></svg>
          <h3>Complete Experience</h3>
          <p>Experience all Acheron adventures in one day</p>
        </div>
      </div>
    </section>
    <section class="testimonial" aria-label="Customer Testimonial">
      <p>"The best day of our lives! The guides were amazing, and the scenery was breathtaking. We can't wait to come back!"</p>
      <cite>- Happy Customer</cite>
    </section>
    <section class="contact" aria-label="Contact Information">
      <div class="contact-item">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><path d="M3 12l2-2 4 4 8-8 2 2"></path></svg>
        <div>
          <strong>Springs of Acheron River</strong><br />
          Glykí, Thesprotia<br />
          46200, Greece
        </div>
      </div>
      <div class="contact-item">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><path d="M22 16.92v3a2 2 0 01-2.18 2 19.79 19.79 0 01-8.63-3.07 19.5 19.5 0 01-6-6 19.79 19.79 0 01-3.07-8.67A2 2 0 015.11 2h3a2 2 0 012 1.72 12.84 12.84 0 00.7 2.81 2 2 0 01-.45 2.11L9 10a16 16 0 006 6l1.36-1.36a2 2 0 012.11-.45 12.84 12.84 0 002.81.7A2 2 0 0122 16.92z"></path></svg>
        <div>
          <strong>+30 698 661 7090</strong><br />
          We speak Greek, English and Finnish.
        </div>
      </div>
      <div class="contact-item">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><path d="M4 4h16v16H4z"></path><path d="M22 4L12 14.01 2 4"></path></svg>
        <div>
          <strong><EMAIL></strong><br />
          During summer season we kindly ask you to call us, as it gets busy and we can be a bit slow to answer to email (1-2 days)
        </div>
      </div>
      <div class="contact-item">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><path d="M12 2a10 10 0 0110 10c0 5.523-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"></path><path d="M12 6v6l4 2"></path></svg>
        <div>
          <strong>Opening Hours:</strong><br />
          10.00 to 18.00 every day<br />
          During season, otherwise we are open on request
        </div>
      </div>
    </section>
  </main>
  <footer>
    &copy; 2025 Pony Club Ecotourism. All rights reserved.
  </footer>
  <script>lucide.createIcons();</script>
</body>
</html>
