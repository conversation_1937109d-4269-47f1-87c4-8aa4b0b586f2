# Google Tag Manager + Google Ads Setup Guide for Next.js 15

## Overview

This guide explains how to configure Google Tag Manager (GTM) and Google Ads conversion tracking for your migrated Next.js 15 application. Your application now sends events via `sendGTMEvent()` instead of direct `gtag()` calls, requiring GTM container configuration to capture and forward these events to Google Ads.

## Prerequisites

✅ **Completed**: Next.js 15 GTM migration (GoogleTagManager component installed)
✅ **Required**: GTM Container ID (`GTM-WVFR8TJX`) configured in `NEXT_PUBLIC_GTM_ID`
✅ **Required**: Existing Google Ads conversion actions from previous setup
✅ **Required**: Google Ads Conversion ID (`AW-XXXXXXXXXX`)

## Part 1: GTM Container Configuration

### Step 1.1: Access Your GTM Container

1. Go to [tagmanager.google.com](https://tagmanager.google.com/)
2. Select your container (`GTM-WVFR8TJX`)
3. You should see your Next.js 15 application is already sending events to the dataLayer

### Step 1.2: Create Built-in Variables

Go to **Variables** → **Configure** and enable these built-in variables:
- ✅ Page URL
- ✅ Page Title
- ✅ Referrer
- ✅ Event (automatically enabled)

### Step 1.3: Create Custom Event Triggers

#### Trigger 1: Booking Click Events
1. Go to **Triggers** → **New**
2. **Trigger Configuration**: Custom Event
3. **Event name**: `booking_click`
4. **Name**: `Event - Booking Click`
5. **Save**

#### Trigger 2: Phone Click Events
1. Go to **Triggers** → **New**
2. **Trigger Configuration**: Custom Event
3. **Event name**: `phone_click`
4. **Name**: `Event - Phone Click`
5. **Save**

#### Trigger 3: Engagement Events (Optional)
1. Go to **Triggers** → **New**
2. **Trigger Configuration**: Custom Event
3. **Event name**: Matches RegEx `scroll_depth|time_on_page`
4. **Name**: `Event - Engagement Tracking`
5. **Save**

### Step 1.4: Create Data Layer Variables

These variables capture the parameters your Next.js app sends:

#### Variable 1: Package Name
1. Go to **Variables** → **New**
2. **Variable Configuration**: Data Layer Variable
3. **Data Layer Variable Name**: `package_name`
4. **Name**: `DLV - Package Name`
5. **Save**

#### Variable 2: Package Value
1. **Variable Configuration**: Data Layer Variable
2. **Data Layer Variable Name**: `value`
3. **Name**: `DLV - Package Value`
4. **Save**

#### Variable 3: Source Page
1. **Variable Configuration**: Data Layer Variable
2. **Data Layer Variable Name**: `source_page`
3. **Name**: `DLV - Source Page`
4. **Save**

#### Variable 4: Device Type
1. **Variable Configuration**: Data Layer Variable
2. **Data Layer Variable Name**: `device_type`
3. **Name**: `DLV - Device Type`
4. **Save**

#### Variable 5: Phone Number
1. **Variable Configuration**: Data Layer Variable
2. **Data Layer Variable Name**: `phone_number`
3. **Name**: `DLV - Phone Number`
4. **Save**

## Part 2: Google Analytics 4 Configuration

### Step 2.1: Create GA4 Configuration Tag

1. Go to **Tags** → **New**
2. **Tag Configuration**: Google Analytics: GA4 Configuration
3. **Measurement ID**: `G-6J3ELVNTQE` (your existing GA4 ID)
4. **Triggering**: All Pages
5. **Name**: `GA4 - Configuration`
6. **Save**

### Step 2.2: Create GA4 Event Tags

#### GA4 Event: Booking Click
1. Go to **Tags** → **New**
2. **Tag Configuration**: Google Analytics: GA4 Event
3. **Configuration Tag**: Select `GA4 - Configuration`
4. **Event Name**: `booking_click`
5. **Event Parameters**:
   - `package_name`: `{{DLV - Package Name}}`
   - `value`: `{{DLV - Package Value}}`
   - `currency`: `EUR`
   - `source_page`: `{{DLV - Source Page}}`
6. **Triggering**: `Event - Booking Click`
7. **Name**: `GA4 - Event - Booking Click`
8. **Save**

#### GA4 Event: Phone Click
1. Go to **Tags** → **New**
2. **Tag Configuration**: Google Analytics: GA4 Event
3. **Configuration Tag**: Select `GA4 - Configuration`
4. **Event Name**: `phone_click`
5. **Event Parameters**:
   - `phone_number`: `{{DLV - Phone Number}}`
   - `device_type`: `{{DLV - Device Type}}`
6. **Triggering**: `Event - Phone Click`
7. **Name**: `GA4 - Event - Phone Click`
8. **Save**
G-6J3ELVNTQE
## Part 3: Google Ads Conversion Tracking

### Step 3.1: Create Google Ads Conversion Tags

You'll create separate conversion tags for each conversion action from your original setup.

#### Conversion Tag: Homepage Package 1 Booking
1. Go to **Tags** → **New**
2. **Tag Configuration**: Google Ads Conversion Tracking
3. **Conversion ID**: `AW-XXXXXXXXXX` (your Google Ads ID)
4. **Conversion Label**: `your_homepage_package1_label` (from original setup)
5. **Conversion Value**: `{{DLV - Package Value}}`
6. **Currency Code**: `EUR`
7. **Order ID**: Leave blank (GTM will auto-generate)
8. **Triggering**: `Event - Booking Click`
9. **Additional Trigger Conditions**:
   - `{{DLV - Source Page}}` equals `homepage`
   - `{{DLV - Package Name}}` contains `Package 1` (adjust based on your package names)
10. **Name**: `Ads - Conversion - Homepage Package 1`
11. **Save**

#### Conversion Tag: Homepage Package 2 Booking
1. **Same as above but**:
2. **Conversion Label**: `your_homepage_package2_label`
3. **Additional Trigger Conditions**:
   - `{{DLV - Source Page}}` equals `homepage`
   - `{{DLV - Package Name}}` contains `Package 2`
4. **Name**: `Ads - Conversion - Homepage Package 2`

#### Conversion Tag: Package Page Package 1 Booking
1. **Same as Package 1 but**:
2. **Conversion Label**: `your_package_page_package1_label`
3. **Additional Trigger Conditions**:
   - `{{DLV - Source Page}}` does not equal `homepage`
   - `{{DLV - Package Name}}` contains `Package 1`
4. **Name**: `Ads - Conversion - Package Page Package 1`

#### Conversion Tag: Package Page Package 2 Booking
1. **Same as Package 2 but**:
2. **Conversion Label**: `your_package_page_package2_label`
3. **Additional Trigger Conditions**:
   - `{{DLV - Source Page}}` does not equal `homepage`
   - `{{DLV - Package Name}}` contains `Package 2`
4. **Name**: `Ads - Conversion - Package Page Package 2`

#### Conversion Tag: Mobile Phone Click
1. Go to **Tags** → **New**
2. **Tag Configuration**: Google Ads Conversion Tracking
3. **Conversion ID**: `AW-XXXXXXXXXX`
4. **Conversion Label**: `your_mobile_phone_label`
5. **Conversion Value**: `0` (or set a value for phone leads)
6. **Currency Code**: `EUR`
7. **Triggering**: `Event - Phone Click`
8. **Additional Trigger Conditions**:
   - `{{DLV - Device Type}}` equals `mobile`
9. **Name**: `Ads - Conversion - Mobile Phone Click`
10. **Save**

#### Conversion Tag: Desktop Phone Click
1. **Same as mobile but**:
2. **Conversion Label**: `your_desktop_phone_label`
3. **Additional Trigger Conditions**:
   - `{{DLV - Device Type}}` equals `desktop`
4. **Name**: `Ads - Conversion - Desktop Phone Click`

## Part 4: Testing & Validation

### Step 4.1: GTM Preview Mode Testing

1. In GTM, click **Preview**
2. Enter your website URL: `http://localhost:3000` (development) or your live URL
3. **Test Booking Clicks**:
   - Click booking buttons on homepage and package pages
   - Verify in GTM debugger that:
     - `booking_click` event fires
     - Correct package_name, value, source_page are captured
     - Appropriate Google Ads conversion tags fire
4. **Test Phone Clicks**:
   - Click phone links on mobile and desktop
   - Verify `phone_click` event fires with correct device_type
   - Verify appropriate phone conversion tags fire

### Step 4.2: Google Ads Conversion Verification

1. Go to Google Ads → **Tools & Settings** → **Conversions**
2. Check each conversion action for recent activity
3. **Test Conversions**:
   - Perform test bookings and phone clicks
   - Wait 15-30 minutes
   - Check if conversions appear in Google Ads
4. **Verify Attribution**:
   - Ensure conversions are attributed to correct campaigns/keywords

### Step 4.3: GA4 Event Verification

1. Go to GA4 → **Reports** → **Realtime**
2. Perform test actions on your site
3. Verify events appear in real-time with correct parameters

## Part 5: Publishing & Monitoring

### Step 5.1: Publish GTM Container

1. In GTM, click **Submit**
2. **Version Name**: `GTM + Google Ads Integration v1.0`
3. **Version Description**: `Migrated from direct gtag to GTM with Google Ads conversion tracking`
4. Click **Publish**

### Step 5.2: Monitor Performance

1. **Google Ads**: Monitor conversion volume and attribution
2. **GA4**: Monitor event tracking and user behavior
3. **GTM**: Use built-in error reporting to catch any issues

## Troubleshooting

### Common Issues

1. **Events not firing**: Check GTM Preview Mode debugger
2. **Conversions not recording**: Verify conversion labels match Google Ads setup
3. **Wrong attribution**: Check trigger conditions and variable mappings
4. **GDPR compliance**: Ensure c15t consent system is working (events should be blocked without consent)

### Debug Commands

In browser console (development mode):
```javascript
// Test GTM events
analyticsDebug.testBooking()
analyticsDebug.testPhoneClick()
analyticsDebug.checkEnvironment()

// Check dataLayer
console.log(window.dataLayer)
```

## Migration Benefits Achieved

✅ **Centralized Management**: All tracking managed in GTM interface
✅ **No Code Deployments**: Change tracking without app updates
✅ **Enhanced Debugging**: GTM Preview Mode for comprehensive testing
✅ **Future-Proof**: Easy integration of new marketing tools
✅ **Performance**: Maintained Partytown compatibility for optimal loading
✅ **GDPR Compliance**: Preserved c15t consent integration

## Part 6: Advanced Configuration

### Step 6.1: Enhanced Ecommerce Tracking (Optional)

For more detailed booking analytics, configure Enhanced Ecommerce:

#### Create Enhanced Ecommerce Variables
1. **Variable**: Data Layer Variable
2. **Name**: `DLV - Ecommerce Items`
3. **Data Layer Variable Name**: `ecommerce.items`

#### Update Booking Event Tag
1. Edit `GA4 - Event - Booking Click`
2. **Event Name**: `begin_checkout`
3. **Event Parameters**:
   - `currency`: `EUR`
   - `value`: `{{DLV - Package Value}}`
   - `items`: `{{DLV - Ecommerce Items}}`

### Step 6.2: Cross-Domain Tracking (If Needed)

If your booking process involves external domains:

1. **GA4 Configuration Tag** → **Fields to Set**:
   - `linker`: `{"domains":["yourdomain.com","booking-partner.com"]}`
2. **Create Cross-Domain Links**:
   - Update booking buttons to include GTM linker parameters

### Step 6.3: Server-Side Tracking Enhancement

For improved data accuracy and iOS 14.5+ compatibility:

1. **Google Ads**: Enable Enhanced Conversions
2. **GTM**: Configure Server-Side GTM container (advanced)
3. **Next.js**: Implement server-side event forwarding

## Part 7: Conversion Value Optimization

### Step 7.1: Dynamic Conversion Values

Update your Next.js components to send actual package prices:

```typescript
// In BookingButton.tsx - ensure accurate pricing
trackBookingClick({
  packageName: "Rafting Adventure",
  packagePrice: 45, // Actual price, not string
  sourcePage: "homepage"
});
```

### Step 7.2: Conversion Value Rules in Google Ads

1. Go to **Google Ads** → **Conversions** → Select conversion action
2. **Value** → **Use different values for each conversion**
3. **Default value**: Set fallback value if GTM doesn't send value

## Part 8: Performance Monitoring

### Step 8.1: GTM Performance Monitoring

1. **GTM** → **Admin** → **Container Settings**
2. Enable **Container Performance Monitoring**
3. Monitor tag firing times and errors

### Step 8.2: Conversion Tracking Health Check

Weekly monitoring checklist:
- [ ] GTM container version is published
- [ ] All conversion tags firing correctly
- [ ] Google Ads showing conversion data
- [ ] GA4 events matching expected volume
- [ ] No GTM errors in console

## Part 9: Data Privacy & Compliance

### Step 9.1: GDPR Compliance Verification

Your c15t consent system should:
1. Block GTM events when consent is denied
2. Allow GTM events when consent is granted
3. Respect user consent preferences across sessions

Test this by:
```javascript
// Deny consent and verify no events fire
// Grant consent and verify events fire normally
```

### Step 9.2: Data Retention Settings

1. **GA4**: Configure data retention (14 months recommended)
2. **Google Ads**: Set conversion window (30 days recommended)
3. **GTM**: No personal data stored in variables

## Part 10: Scaling & Future Enhancements

### Step 10.1: Additional Marketing Platforms

Easy to add through GTM:
- **Facebook Pixel**: Create Facebook Custom Event tags
- **LinkedIn Ads**: Add LinkedIn Insight Tag
- **TikTok Pixel**: Configure TikTok Events API
- **Hotjar**: Add Hotjar tracking code

### Step 10.2: Advanced Attribution

1. **Google Analytics 4**: Enable Google Ads linking
2. **Data-Driven Attribution**: Configure in GA4
3. **Cross-Channel Funnels**: Set up in Google Ads

## Appendix: Event Data Structure

### Booking Click Event
```javascript
{
  event: "booking_click",
  package_name: "Rafting Adventure Package",
  value: 45,
  currency: "EUR",
  source_page: "homepage"
}
```

### Phone Click Event
```javascript
{
  event: "phone_click",
  phone_number: "+30 26650 61314",
  device_type: "mobile"
}
```

### Engagement Events
```javascript
{
  event: "scroll_depth",
  scroll_depth: 75,
  scroll_threshold: "75%"
}

{
  event: "time_on_page",
  time_threshold: 120,
  time_threshold_label: "120s"
}
```

## Quick Reference: GTM Tag Summary

| Tag Name                              | Type           | Trigger                    | Purpose                     |
| ------------------------------------- | -------------- | -------------------------- | --------------------------- |
| GA4 - Configuration                   | GA4 Config     | All Pages                  | Initialize GA4              |
| GA4 - Event - Booking Click           | GA4 Event      | booking_click              | Track bookings in GA4       |
| GA4 - Event - Phone Click             | GA4 Event      | phone_click                | Track phone clicks in GA4   |
| Ads - Conversion - Homepage Package 1 | Ads Conversion | booking_click + conditions | Google Ads conversion       |
| Ads - Conversion - Mobile Phone       | Ads Conversion | phone_click + mobile       | Google Ads phone conversion |

Your Next.js 15 application now has enterprise-level analytics and conversion tracking through Google Tag Manager! 🚀

## Support & Maintenance

For ongoing support:
1. **GTM Community**: [tagmanager.google.com/community](https://tagmanager.google.com/community)
2. **Google Ads Help**: [support.google.com/google-ads](https://support.google.com/google-ads)
3. **GA4 Documentation**: [developers.google.com/analytics/ga4](https://developers.google.com/analytics/ga4)
