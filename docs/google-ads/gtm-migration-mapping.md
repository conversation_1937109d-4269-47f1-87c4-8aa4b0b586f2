# GTM Migration Mapping: Before vs After

## Quick Reference: What Changed

### Environment Variables

| Before (gtag.js) | After (GTM) | Status |
|------------------|-------------|---------|
| `NEXT_PUBLIC_GA_ID=G-6J3ELVNTQE` | ❌ Remove after GTM setup | Configure in GTM |
| `NEXT_PUBLIC_GOOGLE_ADS_CONVERSION_ID=AW-XXXXXXXXXX` | ❌ Remove after GTM setup | Configure in GTM |
| `NEXT_PUBLIC_ADS_LABEL_HOMEPAGE_PACKAGE1` | ❌ Remove after GTM setup | Configure in GTM |
| `NEXT_PUBLIC_ADS_LABEL_HOMEPAGE_PACKAGE2` | ❌ Remove after GTM setup | Configure in GTM |
| `NEXT_PUBLIC_ADS_LABEL_PACKAGE1` | ❌ Remove after GTM setup | Configure in GTM |
| `NEXT_PUBLIC_ADS_LABEL_PACKAGE2` | ❌ Remove after GTM setup | Configure in GTM |
| `NEXT_PUBLIC_ADS_LABEL_PHONE_MOBILE` | ❌ Remove after GTM setup | Configure in GTM |
| `NEXT_PUBLIC_ADS_LABEL_PHONE_DESKTOP` | ❌ Remove after GTM setup | Configure in GTM |
| `NEXT_PUBLIC_GTM_ID=GTM-WVFR8TJX` | ✅ Keep | Required for GTM |

### Component Changes

| Component | Before | After |
|-----------|--------|-------|
| **Layout** | `<GoogleAnalytics gaId={...} />` | `<GoogleTagManager gtmId={...} />` |
| **Layout** | `<GoogleAds conversionId={...} />` | ❌ Removed (handled by GTM) |
| **BookingButton** | Complex tracking with conversion labels | Simple `trackBookingClick()` |
| **PhoneLink** | Device-specific conversion labels | Simple `trackPhoneClick()` |

### Event Tracking Changes

#### Booking Events
**Before (gtag.js):**
```typescript
// Multiple gtag calls with complex logic
gtagEvent("book_now_click", { /* complex params */ });
gtagEvent("begin_checkout", { /* ecommerce data */ });
sendAdsConversion(conversionLabel, packagePrice);
```

**After (GTM):**
```typescript
// Single GTM event
trackGTMEvent({
  event: "booking_click",
  package_name: packageName,
  value: packagePrice,
  currency: "EUR",
  source_page: sourcePage,
});
```

#### Phone Events
**Before (gtag.js):**
```typescript
// Complex device detection and label selection
gtagEvent("phone_click", { /* params */ });
sendAdsConversion(deviceSpecificLabel, 0);
```

**After (GTM):**
```typescript
// Simple event with device info
trackGTMEvent({
  event: "phone_click",
  phone_number: phoneNumber,
  device_type: device,
});
```

## GTM Container Configuration Mapping

### Your Existing Google Ads Conversion Actions → GTM Tags

| Original Conversion Action | GTM Tag Name | GTM Trigger Conditions |
|---------------------------|--------------|------------------------|
| Homepage Package 1 Booking | `Ads - Conversion - Homepage Package 1` | `booking_click` + `source_page = homepage` + `package contains Package 1` |
| Homepage Package 2 Booking | `Ads - Conversion - Homepage Package 2` | `booking_click` + `source_page = homepage` + `package contains Package 2` |
| Package Page Package 1 Booking | `Ads - Conversion - Package Page Package 1` | `booking_click` + `source_page ≠ homepage` + `package contains Package 1` |
| Package Page Package 2 Booking | `Ads - Conversion - Package Page Package 2` | `booking_click` + `source_page ≠ homepage` + `package contains Package 2` |
| Mobile Phone Click | `Ads - Conversion - Mobile Phone Click` | `phone_click` + `device_type = mobile` |
| Desktop Phone Click | `Ads - Conversion - Desktop Phone Click` | `phone_click` + `device_type = desktop` |

### Data Flow Comparison

#### Before (Direct gtag.js)
```
Next.js Component → lib/analytics.ts → window.gtag() → Google Ads
                                   → window.gtag() → Google Analytics
```

#### After (GTM)
```
Next.js Component → lib/analytics.ts → sendGTMEvent() → GTM dataLayer
                                                     → GTM Tags → Google Ads
                                                     → GTM Tags → Google Analytics
                                                     → GTM Tags → Other Platforms
```

## Benefits Achieved

### For Developers
- ✅ **Simplified Code**: Removed complex conversion label logic
- ✅ **Single Event Interface**: One `trackGTMEvent()` function for all platforms
- ✅ **No Environment Variables**: Conversion labels managed in GTM
- ✅ **Better Testing**: GTM Preview Mode vs console.log debugging

### For Marketers
- ✅ **No Code Deployments**: Change tracking without developer involvement
- ✅ **Visual Interface**: GTM's drag-and-drop tag management
- ✅ **Advanced Debugging**: GTM Preview Mode shows exactly what fires
- ✅ **Multi-Platform**: Easy to add Facebook, LinkedIn, TikTok, etc.

### For Performance
- ✅ **Maintained Partytown**: GTM still runs in web worker
- ✅ **Maintained GDPR**: c15t consent system still blocks events
- ✅ **Reduced Bundle Size**: Removed analytics-specific code from components

## Migration Checklist

### Phase 1: Code Migration ✅ COMPLETE
- [x] Updated `app/[locale]/layout.tsx` to use GoogleTagManager
- [x] Refactored `lib/analytics.ts` to use sendGTMEvent
- [x] Updated `components/client/BookingButton.tsx`
- [x] Updated `components/client/PhoneLink.tsx`
- [x] Updated `components/client/EngagementTracker.tsx`
- [x] Removed GoogleAds component
- [x] Fixed all compilation errors

### Phase 2: GTM Configuration 🔄 IN PROGRESS
- [ ] Create GTM triggers for booking_click and phone_click
- [ ] Create GTM variables for package_name, value, source_page, device_type
- [ ] Create GA4 Configuration tag
- [ ] Create GA4 Event tags
- [ ] Create Google Ads Conversion tags (one for each conversion action)
- [ ] Test in GTM Preview Mode
- [ ] Publish GTM container

### Phase 3: Validation & Cleanup 📋 PENDING
- [ ] Verify Google Ads conversions are recording
- [ ] Verify GA4 events are tracking
- [ ] Monitor for 1 week to ensure data accuracy
- [ ] Remove old environment variables from .env.local
- [ ] Update documentation

## Rollback Plan (If Needed)

If issues arise, you can quickly rollback:

1. **Revert Layout Component**:
   ```typescript
   // Change back to:
   <GoogleAnalytics gaId={process.env.NEXT_PUBLIC_GA_ID || "G-6J3ELVNTQE"} />
   ```

2. **Restore Environment Variables**: Uncomment the old variables in `.env.local`

3. **Revert Analytics Functions**: Use git to restore the old `lib/analytics.ts`

However, the GTM approach is superior and should be the permanent solution.

## Next Steps

1. **Follow the main setup guide**: `docs/google-ads/gtm-google-ads-setup-guide.md`
2. **Configure GTM container** with all the tags and triggers
3. **Test thoroughly** using GTM Preview Mode
4. **Monitor conversion data** for accuracy
5. **Clean up old environment variables** once confirmed working

The migration maintains all your existing conversion tracking granularity while providing a much more flexible and maintainable foundation for future growth! 🚀
